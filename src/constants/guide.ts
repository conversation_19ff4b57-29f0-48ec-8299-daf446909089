import { GuideConfig } from '../types/guide';
import { createGuideFromMarkdown } from '../utils/markdownParser';

// 示例引导配置
export const SAMPLE_GUIDE_CONFIG: GuideConfig = {
  guides: [
    {
      id: 'api-creation-guide',
      title: '创建API指南',
      description: '一步步教您如何创建您的第一个API',
      category: 'getting-started',
      difficulty: 'beginner',
      estimatedTime: '5分钟',
      icon: 'api',
      tags: ['API', '快速开始', '基础'],
      prerequisites: {
        type: 'interactive',
        configFile: 'guides/api-creation/prerequisites.json',
        contentFile: 'guides/api-creation/prerequisites.md'
      },
      steps: [
        {
          id: 'step-1',
          title: '进入API管理页面',
          contentFile: 'guides/api-creation/steps/step-1.md',
          target: '[data-guide-id="api-manage-menu"]',
          action: { type: 'click' }
        },
        {
          id: 'step-2',
          title: '创建新API',
          contentFile: 'guides/api-creation/steps/step-2.md',
          target: '[data-guide-id="create-api-button"]',
          action: { type: 'click' }
        }
      ],
      completion: {
        contentFile: 'guides/api-creation/completion.md',
        nextGuides: ['api-test-guide']
      }
    }
  ],
  categories: [
    {
      id: 'getting-started',
      name: '快速开始',
      description: '帮助新用户快速上手',
      order: 1
    }
  ]
};

/**
 * 从 Markdown 文件创建引导配置
 */
export async function createMultiProxyGuideFromMarkdown(): Promise<GuideConfig> {
  try {
    const guide = await createGuideFromMarkdown(
      'src/constants/multi-proxy.md',
      'multi-proxy-markdown',
      '多模型代理指南',
      'AI网关多模型代理功能的完整使用指南',
      {
        category: 'ai-gateway',
        difficulty: 'beginner',
        estimatedTime: '10分钟',
        icon: 'api',
        tags: ['AI网关', '多模型', '代理', '大模型']
      }
    );

    return {
      guides: [guide],
      categories: [
        {
          id: 'ai-gateway',
          name: 'AI网关',
          description: 'AI网关相关功能指南',
          order: 1
        }
      ]
    };
  } catch (error) {
    console.error('Failed to create guide from markdown:', error);
    // 返回默认配置作为后备
    return SAMPLE_GUIDE_CONFIG;
  }
}