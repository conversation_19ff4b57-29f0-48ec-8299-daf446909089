# 场景描述
AI网关能够将外部调用不同大模型的请求，通过统一的调用方式转发到内部对应的大模型上，使得后端模型调度更加灵活；Higress AI网关支持常用的100+个模型的统一协议转换，并支持模型级Fallback。

在大模型评测过程中，多模型代理功能可以构造统一数据集，将模型请求转发到后端模型，验证模型的效果；结合可观测插件，能够清晰地追踪不同模型的链路。
# 配置大模型服务
 1. AI服务提供者管理界面，可以配置已集成供应商的API-KEY。当前已集成的供应商有阿里云、DeepSeek、Azure OpenAI、OpenAI、豆包等。这里我们配置上通义千问及DeepSeek的多模型代理。
 2. 在AI路由管理中，为DeepSeek路由进行降级配置；当请求目标服务失败（如限流、访问失败等）时，降级到阿里云qwen-turbo模型。

# 调试
打开系统自带命令行，通过以下命令进行请求（如HTTP服务未部署在8080端口上，修改为对应端口即可）

# 结果观测
在AI监控面板界面，可以对AI请求进行观测。观测指标包括每秒输入输出Token数量、各供应商/模型Token使用数量等。