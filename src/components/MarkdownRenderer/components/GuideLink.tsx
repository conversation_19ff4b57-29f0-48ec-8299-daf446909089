import React from 'react';
import { guideEventBus } from '../../../services/GuideEventBus';

interface GuideLinkProps {
  type?: 'selector' | 'guideId' | 'text';
  name?: string;
  children?: React.ReactNode;
}

const GuideLink: React.FC<GuideLinkProps> = ({
  type,
  name,
  children
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    console.log('🔗 GuideLink clicked:', { type, name, children });

    switch (type) {
      case 'selector':
        window.guideHighlightService.highlightElement({
          selector: name,
        }, {
          borderColor: '#1890ff',
          borderWidth: 3,
        });
        break;
      case 'guideId':
        window.guideHighlightService.highlightElement({
          guideId: name,
        }, {
          borderColor: '#1890ff',
          borderWidth: 3,
        });
        break;
      case 'text':
        window?.guideHighlightService?.highlightElement({
          text: name,
        }, {
          borderColor: '#faad14',
          borderWidth: 3,
        });
        break;
      default:
        break;
    }

    // // 发送本地事件
    // onEvent?.({
    //   type: 'linkClick',
    //   data: { href, action, target },
    // });
  };

  return (
    <a
      href={'#'}
      onClick={handleClick}
      className="guide-link"
      style={{
        color: '#1890ff',
        textDecoration: 'underline',
        cursor: 'pointer'
      }}
    >
      {children}
    </a>
  );
};

export default GuideLink; 