import React, { useState, useEffect, useMemo, useContext } from 'react';
import { Button } from '@ali/xconsole';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useAlfaMicroAppContext } from '@ali/xconsole/alfa';
import { MarkdownRendererProps } from '../../types/guide';
import GuideSelect from './components/GuideSelect';
import GuideButton from './components/GuideButton';
import GuideLink from './components/GuideLink';
import './index.less';

/**
 * 增强的Markdown处理器
 */
class EnhancedMarkdownProcessor {
  private context: Record<string, any> = {};

  constructor(context: Record<string, any> = {}) {
    this.context = context;
  }

  /**
   * 处理模板变量
   */
  processTemplate(content: string): string {
    return content.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const value = this.getNestedValue(this.context, variable.trim());
      return value !== undefined ? String(value) : match;
    });
  }

   /**
   * 解析自定义标签
   */
   parseCustomTags(content: string): string {
    console.log('🔍 parseCustomTags input:', content);

    // 处理 guide- 前缀的自闭合标签
    const guideSelfClosingTagRegex = /<guide-(\w+)([^>]*?)\/>/g;
    content = content.replace(guideSelfClosingTagRegex, (match, tagName, attributes) => {
      console.log('🔧 Found guide self-closing tag:', { match, tagName, attributes });
      return `<guide-${tagName}${attributes}></guide-${tagName}>`;
    });

    // 处理其他自定义组件的自闭合标签
    const customSelfClosingTagRegex = /<(CustomComponent)([^>]*?)\/>/g;
    content = content.replace(customSelfClosingTagRegex, (match, tagName, attributes) => {
      console.log('🔧 Found custom self-closing tag:', { match, tagName, attributes });
      return `<${tagName}${attributes}></${tagName}>`;
    });

    // 处理 guide- 前缀的标准标签
    const guideTagRegex = /<guide-(\w+)([^>]*?)>(.*?)<\/guide-\1>/gs;
    content = content.replace(guideTagRegex, (match, tagName, attributes, children) => {
      console.log('🔧 Found guide standard tag:', { match, tagName, attributes, children });
      const props = this.parseAttributes(attributes);
      console.log('🔧 Parsed props:', props);

      const componentData = {
        tagName: `guide-${tagName}`,
        props,
        children: children.trim()
      };

      const placeholder = `<!--GUIDE-COMPONENT:${JSON.stringify(componentData)}-->`;
      console.log('🔧 Created placeholder:', placeholder);
      return placeholder;
    });

    // 处理其他自定义组件的标准标签
    const customTagRegex = /<(CustomComponent)([^>]*?)>(.*?)<\/\1>/gs;
    const result = content.replace(customTagRegex, (match, tagName, attributes, children) => {
      console.log('🔧 Found custom standard tag:', { match, tagName, attributes, children });
      const props = this.parseAttributes(attributes);
      console.log('🔧 Parsed props:', props);

      const componentData = {
        tagName,
        props,
        children: children.trim()
      };

      const placeholder = `<!--GUIDE-COMPONENT:${JSON.stringify(componentData)}-->`;
      console.log('🔧 Created placeholder:', placeholder);
      return placeholder;
    });

    console.log('🔍 parseCustomTags output:', result);
    return result;
  }


  /**
   * 获取嵌套对象值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

 
  /**
   * 解析HTML属性
   */
  private parseAttributes(attributeString: string): Record<string, any> {
    const attrs: Record<string, any> = {};
    // 支持带引号和不带引号的属性值
    const attrRegex = /(\w+(?:-\w+)*)=(?:["']([^"']*)["']|([^\s>]+))/g;

    console.log('🔧 Parsing attributes:', attributeString);

    let match;
    while ((match = attrRegex.exec(attributeString)) !== null) {
      const [, key, quotedValue, unquotedValue] = match;
      const value = quotedValue || unquotedValue;
      const camelKey = this.camelCase(key);
      attrs[camelKey] = value;
      console.log('🔧 Attribute parsed:', { key, camelKey, value });
    }

    console.log('🔧 Final attributes:', attrs);
    return attrs;
  }

  /**
   * 转换为驼峰命名
   */
  private camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }


}

/**
 * 自定义组件映射
 */
const customComponents = {
  'guide-select': GuideSelect,
  'guide-button': GuideButton,
  'guide-link': GuideLink,
  'CustomComponent': (props: any) => {
    console.log("========", props);
    return <div>select</div>;
  },
};

/**
 * Markdown渲染器组件
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  context = {},
  onComponentEvent
}) => {
  const { appProps } = useAlfaMicroAppContext();
  // const [processedContent, setProcessedContent] = useState('');
  const [components, setComponents] = useState<React.ReactNode[]>([]);

  const processor = useMemo(() => new EnhancedMarkdownProcessor(context), [context]);

  // useEffect(() => {
  //   // 处理模板变量
  //   let processed = processor.processTemplate(content);
  //   console.log("8888888",processed,content)

  //   // 处理自定义标签
  //   // processed = processor.parseCustomTags(processed);
  //   setProcessedContent(processed);
  //   console.log("9999999",processed)
  // }, [content, context, processor]);

  const rendererComponents = {
    // 代码高亮
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={tomorrow}
          language={match[1]}
          PreTag="div"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },

    // 处理链接
    a: ({ href, children, title, ...props }: any) => {
      console.log('🔗 ======:', href, children, props);
      // 检查是否是特殊的引导链接
      if (href?.startsWith('highlight')) {
        const parts = href.split('_');
        const highlightType = parts[1];
        const text = title;

        console.log('🔗 解析引导链接:', { href, highlightType, text });

        return (
          <GuideLink
            type={highlightType}
            name={text}
          >
            {children}
          </GuideLink>
        );
      }

      if (href?.startsWith('goToUrl')) {
        return (
          <a
            href={'#'}
            style={{
              color: '#1890ff',
              cursor: 'pointer',
            }}
            onClick={(e) => {
              e.preventDefault();
              appProps.emitter.emit('goToUrl', {
                path: title,
              });
            }}
          >
            {children}
          </a>
        )
      }

      // 外部链接在新窗口打开
      if (href?.startsWith('http')) {
        return (
          <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
            {children}
          </a>
        );
      }

      return <a href={href} {...props}>{children}</a>;
    },

    // // 处理 HTML 内容（包含自定义组件）
    // html({ children, ...props }) {
    //   const htmlContent = String(children);
    //   console.log('🔍 HTML content:', htmlContent);

    //   // 检查是否包含组件占位符
    //   const componentRegex = /<!--GUIDE-COMPONENT:(.*?)-->/g;
    //   const parts = [];
    //   let lastIndex = 0;
    //   let match;

    //   while ((match = componentRegex.exec(htmlContent)) !== null) {
    //     // 添加组件前的文本
    //     if (match.index > lastIndex) {
    //       parts.push(htmlContent.slice(lastIndex, match.index));
    //     }

    //     // 解析并渲染组件
    //     try {
    //       const componentData = JSON.parse(match[1]);
    //       console.log('🔧 Component data:', componentData);

    //       // 尝试不同的组件名查找方式
    //       let Component = customComponents[componentData.tagName as keyof typeof customComponents];

    //       // 如果没找到，尝试添加 guide- 前缀
    //       if (!Component && !componentData.tagName.startsWith('guide-')) {
    //         Component = customComponents[`guide-${componentData.tagName}` as keyof typeof customComponents];
    //       }

    //       console.log('🔧 Found component:', Component ? 'Yes' : 'No', 'for tagName:', componentData.tagName);

    //       if (Component) {
    //         parts.push(
    //           <Component
    //             key={match.index}
    //             {...componentData.props}
    //             onEvent={onComponentEvent}
    //           >
    //             {componentData.children}
    //           </Component>
    //         );
    //       } else {
    //         console.warn('⚠️ Component not found for tagName:', componentData.tagName);
    //         parts.push(match[0]);
    //       }
    //     } catch (error) {
    //       console.error('Failed to parse component data:', error);
    //       parts.push(match[0]);
    //     }

    //     lastIndex = match.index + match[0].length;
    //   }

    //   // 添加最后的文本
    //   if (lastIndex < htmlContent.length) {
    //     parts.push(htmlContent.slice(lastIndex));
    //   }

    //   return parts.length > 1 ? <>{parts}</> : <div dangerouslySetInnerHTML={{ __html: htmlContent }} />;
    // },

    // 处理自定义的 GuideLink 组件
    GuideLink({ target, children }) {
      return (
        <a href={`guide:${target}`} target="_self">
          {children}
        </a>
      );
    },

    customComponent(props) {
      console.log("========", props);
      return (
        <div>select</div>
      )
    }
  };

  return (
    <div className="guide-markdown-renderer">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={rendererComponents}
      >
        {content}
      </ReactMarkdown>
    </div>
  );

};

export default MarkdownRenderer;
