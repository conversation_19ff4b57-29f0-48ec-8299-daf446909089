import React, { useState, useEffect } from 'react';
import GuidePanel from '../GuidePanel';
import { Guide } from '../../types/guide';
import { createMultiProxyGuideFromMarkdown } from '../../constants/guide';

/**
 * 演示如何使用 Markdown 文件自动生成引导步骤的示例组件
 */
const MarkdownGuideExample: React.FC = () => {
  const [guide, setGuide] = useState<Guide | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    loadGuideFromMarkdown();
  }, []);

  const loadGuideFromMarkdown = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const guideConfig = await createMultiProxyGuideFromMarkdown();
      const loadedGuide = guideConfig.guides[0];
      
      setGuide(loadedGuide);
      console.log('✅ 成功从 Markdown 创建引导:', loadedGuide);
    } catch (err) {
      console.error('❌ 创建引导失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  const handleShowGuide = () => {
    setVisible(true);
  };

  const handleCloseGuide = () => {
    setVisible(false);
  };

  const handleCompleteGuide = () => {
    console.log('🎉 引导完成!');
    setVisible(false);
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div>正在从 Markdown 文件加载引导...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <div>加载失败: {error}</div>
        <button onClick={loadGuideFromMarkdown} style={{ marginTop: '10px' }}>
          重试
        </button>
      </div>
    );
  }

  if (!guide) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div>未找到引导配置</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>Markdown 引导示例</h2>
      <div style={{ marginBottom: '20px' }}>
        <p><strong>引导标题:</strong> {guide.title}</p>
        <p><strong>描述:</strong> {guide.description}</p>
        <p><strong>步骤数量:</strong> {guide.steps?.length || 0}</p>
        <p><strong>预计时间:</strong> {guide.estimatedTime}</p>
        <p><strong>难度:</strong> {guide.difficulty}</p>
        <p><strong>标签:</strong> {guide.tags.join(', ')}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>步骤预览:</h3>
        <ol>
          {guide.steps?.map((step, index) => (
            <li key={step.id} style={{ marginBottom: '8px' }}>
              <strong>{step.title}</strong>
              {step.content && (
                <div style={{ 
                  marginTop: '4px', 
                  padding: '8px', 
                  backgroundColor: '#f5f5f5', 
                  borderRadius: '4px',
                  fontSize: '0.9em',
                  maxHeight: '100px',
                  overflow: 'hidden'
                }}>
                  {step.content.substring(0, 200)}
                  {step.content.length > 200 && '...'}
                </div>
              )}
            </li>
          ))}
        </ol>
      </div>

      <div>
        <button 
          onClick={handleShowGuide}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          开始引导
        </button>
      </div>

      {visible && guide && (
        <GuidePanel
          guide={guide}
          visible={visible}
          position="right"
          width={400}
          onClose={handleCloseGuide}
          onComplete={handleCompleteGuide}
          context={{
            regionId: 'cn-shanghai',
            gatewayId: 'gw-001'
          }}
        />
      )}
    </div>
  );
};

export default MarkdownGuideExample;
