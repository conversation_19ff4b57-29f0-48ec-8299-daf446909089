import { Guide, Step } from '../types/guide';

export interface ParsedMarkdownStep {
  id: string;
  title: string;
  content: string;
  order: number;
}

/**
 * 解析 Markdown 内容，根据 h1 标签分割步骤
 */
export function parseMarkdownToSteps(markdownContent: string): ParsedMarkdownStep[] {
  const steps: ParsedMarkdownStep[] = [];
  
  // 按行分割内容
  const lines = markdownContent.split('\n');
  
  let currentStep: ParsedMarkdownStep | null = null;
  let currentContent: string[] = [];
  let stepOrder = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检查是否是 h1 标签
    if (line.startsWith('# ')) {
      // 如果有当前步骤，先保存它
      if (currentStep) {
        currentStep.content = currentContent.join('\n').trim();
        steps.push(currentStep);
      }
      
      // 创建新步骤
      const title = line.substring(2).trim(); // 移除 "# " 前缀
      currentStep = {
        id: `step-${stepOrder + 1}`,
        title,
        content: '',
        order: stepOrder
      };
      
      currentContent = [];
      stepOrder++;
    } else {
      // 将内容添加到当前步骤
      currentContent.push(line);
    }
  }
  
  // 保存最后一个步骤
  if (currentStep) {
    currentStep.content = currentContent.join('\n').trim();
    steps.push(currentStep);
  }
  
  return steps;
}

/**
 * 从 Markdown 文件路径创建 Guide 对象
 */
export async function createGuideFromMarkdown(
  markdownFilePath: string,
  guideId: string,
  title: string,
  description: string,
  options: {
    category?: string;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    estimatedTime?: string;
    icon?: string;
    tags?: string[];
  } = {}
): Promise<Guide> {
  try {
    // 加载 markdown 文件内容
    const markdownContent = await loadMarkdownFile(markdownFilePath);
    
    // 解析步骤
    const parsedSteps = parseMarkdownToSteps(markdownContent);
    
    // 转换为 Step 对象
    const steps: Step[] = parsedSteps.map(parsedStep => ({
      id: parsedStep.id,
      title: parsedStep.title,
      contentFile: '', // 不使用文件，直接使用内容
      content: parsedStep.content // 添加内容字段
    }));
    
    // 创建 Guide 对象
    const guide: Guide = {
      id: guideId,
      title,
      description,
      category: options.category || 'general',
      difficulty: options.difficulty || 'beginner',
      estimatedTime: options.estimatedTime || '5分钟',
      icon: options.icon,
      tags: options.tags || [],
      steps
    };
    
    return guide;
  } catch (error) {
    console.error('Failed to create guide from markdown:', error);
    throw new Error(`无法从 ${markdownFilePath} 创建引导: ${error.message}`);
  }
}

/**
 * 加载 Markdown 文件内容
 * 在实际项目中，这里应该是真实的文件加载逻辑
 */
async function loadMarkdownFile(filePath: string): Promise<string> {
  // 模拟文件加载延迟
  await new Promise(resolve => setTimeout(resolve, 100));

  // 根据文件路径返回对应的内容
  if (filePath.includes('multi-proxy.md')) {
    // 直接返回 multi-proxy.md 的实际内容
    return `# 场景描述
AI网关能够将外部调用不同大模型的请求，通过统一的调用方式转发到内部对应的大模型上，使得后端模型调度更加灵活；Higress AI网关支持常用的100+个模型的统一协议转换，并支持模型级Fallback。

在大模型评测过程中，多模型代理功能可以构造统一数据集，将模型请求转发到后端模型，验证模型的效果；结合可观测插件，能够清晰地追踪不同模型的链路。

# 配置大模型服务
1. AI服务提供者管理界面，可以配置已集成供应商的API-KEY。当前已集成的供应商有阿里云、DeepSeek、Azure OpenAI、OpenAI、豆包等。这里我们配置上通义千问及DeepSeek的多模型代理。

2. 在AI路由管理中，为DeepSeek路由进行降级配置；当请求目标服务失败（如限流、访问失败等）时，降级到阿里云qwen-turbo模型。

<guide-button
  action="highlight"
  type="primary"
  text="配置AI服务"
  highlight-target="ai-service-config"
/>

# 调试
打开系统自带命令行，通过以下命令进行请求（如HTTP服务未部署在8080端口上，修改为对应端口即可）

\`\`\`bash
curl -X POST http://localhost:8080/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "deepseek-chat",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己"}
    ]
  }'
\`\`\`

# 结果观测
在AI监控面板界面，可以对AI请求进行观测。观测指标包括每秒输入输出Token数量、各供应商/模型Token使用数量等。

通过[监控面板](goToUrl "/ai/monitoring")可以查看详细的请求统计信息。`;
  }

  // 默认返回示例内容
  return `# 示例步骤 1
这是从 ${filePath} 加载的示例内容。

## 操作说明
请按照以下步骤进行操作：

1. 点击左侧菜单
2. 选择相应的功能
3. 完成配置

<guide-button
  action="highlight"
  type="primary"
  text="开始操作"
  highlight-target="start-button"
/>

# 示例步骤 2
这是第二个步骤的内容。

## 验证结果
完成上一步后，您应该能看到相应的结果。

# 示例步骤 3
这是第三个步骤的内容。

## 总结
恭喜您完成了所有步骤！`;
}

/**
 * 获取步骤的实际内容
 * 如果步骤有直接的内容，返回内容；否则尝试从文件加载
 */
export async function getStepContent(step: Step, context: Record<string, any> = {}): Promise<string> {
  // 如果步骤有直接的内容字段，使用它
  if ('content' in step && step.content) {
    return processTemplate(step.content, context);
  }
  
  // 否则从文件加载（保持向后兼容）
  if (step.contentFile) {
    const content = await loadMarkdownFile(step.contentFile);
    return processTemplate(content, context);
  }
  
  return '暂无内容';
}

/**
 * 处理模板变量
 */
function processTemplate(content: string, context: Record<string, any>): string {
  return content.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
    const value = getNestedValue(context, variable.trim());
    return value !== undefined ? String(value) : match;
  });
}

/**
 * 获取嵌套对象值
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}
