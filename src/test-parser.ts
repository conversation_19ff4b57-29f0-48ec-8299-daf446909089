// 测试 markdown 解析功能
import { parseMarkdownToSteps, createGuideFromMarkdown } from './utils/markdownParser';

// 测试用的 markdown 内容（来自 multi-proxy.md）
const testMarkdownContent = `# 场景描述
AI网关能够将外部调用不同大模型的请求，通过统一的调用方式转发到内部对应的大模型上，使得后端模型调度更加灵活；Higress AI网关支持常用的100+个模型的统一协议转换，并支持模型级Fallback。

在大模型评测过程中，多模型代理功能可以构造统一数据集，将模型请求转发到后端模型，验证模型的效果；结合可观测插件，能够清晰地追踪不同模型的链路。
# 配置大模型服务
 1. AI服务提供者管理界面，可以配置已集成供应商的API-KEY。当前已集成的供应商有阿里云、DeepSeek、Azure OpenAI、OpenAI、豆包等。这里我们配置上通义千问及DeepSeek的多模型代理。
 2. 在AI路由管理中，为DeepSeek路由进行降级配置；当请求目标服务失败（如限流、访问失败等）时，降级到阿里云qwen-turbo模型。

# 调试
打开系统自带命令行，通过以下命令进行请求（如HTTP服务未部署在8080端口上，修改为对应端口即可）

# 结果观测
在AI监控面板界面，可以对AI请求进行观测。观测指标包括每秒输入输出Token数量、各供应商/模型Token使用数量等。`;

// 测试解析功能
function testMarkdownParsing() {
  console.log('🧪 开始测试 Markdown 解析功能...');
  
  const steps = parseMarkdownToSteps(testMarkdownContent);
  
  console.log(`✅ 解析完成，共找到 ${steps.length} 个步骤:`);
  
  steps.forEach((step, index) => {
    console.log(`\n📝 步骤 ${index + 1}:`);
    console.log(`   ID: ${step.id}`);
    console.log(`   标题: ${step.title}`);
    console.log(`   内容长度: ${step.content.length} 字符`);
    console.log(`   内容预览: ${step.content.substring(0, 100)}...`);
  });
  
  return steps;
}

// 测试创建引导配置
async function testCreateGuide() {
  console.log('\n🧪 开始测试创建引导配置...');
  
  try {
    const guide = await createGuideFromMarkdown(
      'src/constants/multi-proxy.md',
      'multi-proxy-test',
      '多模型代理测试',
      '测试从 Markdown 创建引导配置',
      {
        category: 'test',
        difficulty: 'beginner',
        estimatedTime: '5分钟',
        tags: ['测试', 'Markdown']
      }
    );
    
    console.log('✅ 引导配置创建成功:');
    console.log(`   ID: ${guide.id}`);
    console.log(`   标题: ${guide.title}`);
    console.log(`   步骤数量: ${guide.steps?.length || 0}`);
    
    guide.steps?.forEach((step, index) => {
      console.log(`\n   步骤 ${index + 1}: ${step.title}`);
      if (step.content) {
        console.log(`     内容长度: ${step.content.length} 字符`);
      }
    });
    
    return guide;
  } catch (error) {
    console.error('❌ 创建引导配置失败:', error);
    throw error;
  }
}

// 运行测试
export async function runTests() {
  try {
    // 测试解析功能
    const steps = testMarkdownParsing();
    
    // 验证解析结果
    if (steps.length !== 4) {
      throw new Error(`期望 4 个步骤，实际得到 ${steps.length} 个`);
    }
    
    const expectedTitles = ['场景描述', '配置大模型服务', '调试', '结果观测'];
    steps.forEach((step, index) => {
      if (step.title !== expectedTitles[index]) {
        throw new Error(`步骤 ${index + 1} 标题不匹配，期望 "${expectedTitles[index]}"，实际 "${step.title}"`);
      }
    });
    
    console.log('✅ 解析功能测试通过');
    
    // 测试创建引导配置
    const guide = await testCreateGuide();
    
    console.log('\n🎉 所有测试通过！');
    return { steps, guide };
  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runTests().catch(console.error);
}
