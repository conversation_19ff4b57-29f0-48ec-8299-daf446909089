<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 引导测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.secondary {
            background: #6c757d;
        }
        .test-button.secondary:hover {
            background: #545b62;
        }
        #widget-container {
            min-height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
        }
        .highlight-targets {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .highlight-target {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            cursor: pointer;
        }
        .highlight-target:hover {
            background: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Markdown 引导功能测试</h1>
        <p>这个页面用于测试基于 Markdown 文件自动生成引导步骤的功能。</p>
        
        <div class="test-buttons">
            <button class="test-button" onclick="loadMarkdownGuide()">
                加载 Markdown 引导
            </button>
            <button class="test-button secondary" onclick="clearWidget()">
                清除组件
            </button>
        </div>

        <div id="widget-container">
            <p>点击上方按钮加载 Markdown 引导组件...</p>
        </div>

        <div class="highlight-targets">
            <h3>测试高亮目标元素：</h3>
            <div class="highlight-target" data-guide-id="ai-service-config">AI服务配置</div>
            <div class="highlight-target" data-guide-id="start-button">开始按钮</div>
            <div class="highlight-target" data-guide-id="monitoring-panel">监控面板</div>
            <div class="highlight-target" data-guide-id="api-management">API管理</div>
        </div>
    </div>

    <script>
        function loadMarkdownGuide() {
            const container = document.getElementById('widget-container');
            container.innerHTML = '<div id="markdown-guide-root"></div>';
            
            // 模拟加载 React 组件
            // 在实际项目中，这里会加载真实的 React 组件
            container.innerHTML = `
                <div style="padding: 20px; border: 2px dashed #007bff; border-radius: 8px;">
                    <h3>🚀 Markdown 引导组件已加载</h3>
                    <p><strong>功能说明：</strong></p>
                    <ul>
                        <li>✅ 自动解析 multi-proxy.md 文件</li>
                        <li>✅ 根据 H1 标签自动分割步骤</li>
                        <li>✅ 生成 4 个引导步骤：</li>
                        <ol>
                            <li>场景描述</li>
                            <li>配置大模型服务</li>
                            <li>调试</li>
                            <li>结果观测</li>
                        </ol>
                        <li>✅ 支持自定义组件（guide-button, guide-link 等）</li>
                        <li>✅ 支持模板变量替换</li>
                    </ul>
                    <p><strong>使用方式：</strong></p>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">
import { createMultiProxyGuideFromMarkdown } from './constants/guide';

// 创建引导配置
const guideConfig = await createMultiProxyGuideFromMarkdown();
const guide = guideConfig.guides[0];

// 使用 GuidePanel 组件
&lt;GuidePanel
  guide={guide}
  visible={true}
  position="right"
  width={400}
  onClose={handleClose}
  onComplete={handleComplete}
/&gt;
                    </pre>
                    <button onclick="simulateGuideStart()" style="margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        模拟开始引导
                    </button>
                </div>
            `;
        }

        function clearWidget() {
            const container = document.getElementById('widget-container');
            container.innerHTML = '<p>组件已清除。点击上方按钮重新加载...</p>';
        }

        function simulateGuideStart() {
            alert('🎉 引导已开始！\n\n在实际项目中，这里会显示 GuidePanel 组件，用户可以按步骤进行操作。\n\n步骤包括：\n1. 场景描述\n2. 配置大模型服务\n3. 调试\n4. 结果观测');
        }

        // 模拟高亮服务
        window.guideHighlightService = {
            highlightElement: function(target, options) {
                console.log('🎯 高亮元素:', target, options);
                
                let element = null;
                if (target.selector) {
                    element = document.querySelector(target.selector);
                } else if (target.guideId) {
                    element = document.querySelector(`[data-guide-id="${target.guideId}"]`);
                } else if (target.text) {
                    const elements = Array.from(document.querySelectorAll('*'))
                        .filter(el => el.textContent && el.textContent.includes(target.text));
                    element = elements[0];
                }
                
                if (element) {
                    element.style.outline = '2px solid #007bff';
                    element.style.outlineOffset = '2px';
                    element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                    
                    setTimeout(() => {
                        element.style.outline = '';
                        element.style.outlineOffset = '';
                        element.style.backgroundColor = '';
                    }, 3000);
                }
            },
            clearHighlight: function() {
                console.log('🧹 清除高亮');
            }
        };
    </script>
</body>
</html>
